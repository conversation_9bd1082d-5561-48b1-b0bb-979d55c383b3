<template>
  <div class="date-picker">
    <div class="date-picker-types">
      <span v-show="showText">选择时间：</span>
      <el-select v-model="dateType" size="small" value-key="label" @change="handleDateChange()">
        <el-option v-for="dt in dateTypes" :key="dt.value" :label="dt.label" :value="dt" />
      </el-select>
    </div>
    <!-- 季度组件 quarter-->
    <date-quarter
      v-if="dateType.value=='quarter'"
      v-model="dates.quarter__date"
      @change="handleDateChange()"
    />
    <!-- 自定义周组件 monthweek-->
    <date-month-week
      v-else-if="dateType.value=='monthweek'"
      v-model="dates.monthweek__date"
      @change="handleDateChange()"
    />
    <!-- <el-date-picker
      v-else-if="dateType.value=='month'"
      v-model="dates.month__date"
      type="month"
      size="small"
      format="yyyy年MM月"
      :clearable="false"
      placeholder="选择月份"
      :picker-options="pickOptions"
      @input="handleDateChange"
    /> -->
    <!-- < 年月日组件 -->
    <el-date-picker
      v-else
      :key="dateType.label"
      v-model="dates[`${dateType.value}__date`]"
      size="small"
      :type="dateType.value"
      :format="format[dateType.value]"
      :clearable="clearble"
      :picker-options="pickerOptions"
      @change="handleDateChange()"
    />
  </div>
</template>

<script>
import DateQuarter from './DateQuarter.vue'
import DateMonthWeek from './DateMonthWeek.vue'
import Common from 'bj_src/lib/date'

export default {
  name: 'DatePicker',
  components: {
    DateQuarter,
    DateMonthWeek
  },
  props: {
    showText: {
      type: Boolean,
      default: false
    },
    defaultType: {
      type: String,
      default: '月'
    },
    // 目前只处理了 年，月，日的默认值
    // 自定义季度，周，已经范围筛选组件未做处理，后续可做优化
    defaultDate: {
      type: Object,
      default: () => ({}) // {'月': new Date(),'日': new Date()}
    },
    dateTypes: {
      type: Array,
      default: () => [
        // date daterange month monthrange year -- elementUI 自带的时间类型
        // quarter（季度） monthweek（每月第几周）-- 自定义的时间类型
        { label: '日', value: 'daterange' }, // date
        { label: '月', value: 'month' }, // monthrange
        // { label: "周", value: "monthweek" },
        { label: '季度', value: 'quarter' }
        // { label: '年', value: 'year' },
      ]
    },
    format: {
      type: Object,
      default: () => ({
        date: 'yyyy-MM-dd',
        month: 'yyyy年MM月',
        year: 'yyyy年',
        quarter: 'yyyy年Q季度',
        daterange: 'yyyy-MM-dd'
      })
    },
    valueFormat: {
      type: Object,
      default: () => ({
        date: 'yyyy-MM-dd',
        month: 'yyyy-MM',
        year: 'yyyy',
        quarter: 'yyyy-MM',
        daterange: 'yyyy-MM-dd'
      })
    },
    clearble: {
      type: Boolean,
      default: false
    },
    // 是否初始化就
    initTriggerChange: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const today = new Date()
    const curQ = new Date(today.setMonth(today.getMonth() / 3)) // 季度Date
    const [dateType] = this.dateTypes.filter(
      ({ label }) => label == this.defaultType
    )
    // 30天间隔
    const day30 = 30 * 24 * 3600 * 1000

    return {
      dateType: dateType || this.dateTypes[0] || { value: 'date', label: '日' },
      dates: {
        // quarter__date: Common.formatDate(curQ, "yyyy-MM")
      },
      // daterange的pickerOptions，选择时间范围补超过30天
      pickerMinDate: null,
      pickerMaxdate: null,
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          if (minDate && this.pickerMinDate) {
            this.pickerMinDate = null
          } else if (minDate) {
            this.pickerMinDate = minDate.getTime()
          }
        },
        disabledDate: time => {
          if (this.pickerMinDate) {
            return (
              time.getTime() > Date.now() ||
              time.getTime() > this.pickerMinDate + day30 ||
              time.getTime() < this.pickerMinDate - day30
            )
          }
          return time.getTime() > Date.now()
        }
      }
    }
  },
  watch: {
    defaultDate() {
      this.initDateValues()
    },
    dateTypes() {
      this.changeDateTypes()
    },
    defaultType() {
      this.setDateType()
    }
  },
  created() {
    this.initDateValues()
    if (this.initTriggerChange) this.handleDateChange()
  },
  methods: {
    initDateValues() {
      this.dateTypes.forEach((item) => {
        this.setDateTypeValue(item)
      })
    },
    setDateType() {
      const [dateType] = this.dateTypes.filter(
        ({ label }) => label == this.defaultType
      )
      this.dateType = dateType || this.dateTypes[0] || { value: 'date', label: '日' }
    },
    setDateTypeValue(dateType) {
      const { value, label } = dateType
      // 季度
      if (value === 'quarter') {
        const today = new Date()
        const curQ = new Date(today.setMonth(today.getMonth() / 3)) // 季度Date
        const initValues = this.defaultDate[label] ? this.defaultDate[label].replace('-Q', '-0') : Common.formatDate(curQ, 'yyyy-MM')
        this.dates[`${value}__date`] = initValues
        // 自定义周组件
      } else if (value === 'monthweek') {
        const today = new Date()
        const date = today.getDate()
        const year = today.getFullYear()
        const month = today.getMonth()
        // 07 14 21 月最后一天
        const weekDate = date > 7 * 3 ? new Date(year, month + 1, 0).getDate() : (Math.floor(date / 7) + 1) * 7
        const curWeek = new Date(year, month, weekDate) //
        const initValues = this.defaultDate[label] ? this.defaultDate[label] : Common.formatDate(curWeek, 'yyyy-MM-dd')
        this.dates[`${value}__date`] = initValues
        // todo 还未设置默认值
      } else if (value.indexOf('range') > -1) {
        const start = new Date()
        // 提前14天
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 14)
        this.dates[`${value}__date`] = [start, new Date()]
      } else {
        // 日月默认值
        const initValues = this.defaultDate[label]
          ? typeof this.defaultDate[label] === 'string' ? new Date(this.defaultDate[label].replace(/-/g, '/')) : this.defaultDate[label]
          : new Date()
        this.dates[`${value}__date`] = initValues
      }
    },
    handleDateChange(eventName = 'change') {
      if (!this.dateTypes.length) return
      const { value, label } = this.dateType
      if (!value) return
      if (!this.dates[`${value}__date`]) return
      if (value == 'quarter') {
        // 季度组件返回的值的格式就是yyyy-MM,不是日期对象
        this.$emit(eventName, [label, this.dates[`${value}__date`]])
      } else if (value === 'monthweek') {
        // 周组件返回的值的格式就是yyyy-MM-dd,不是日期对象
        this.$emit(eventName, [label, this.dates[`${value}__date`]])
      } else if (value.indexOf('range') > -1) {
        this.$emit(eventName, [
          label,
          this.dates[`${value}__date`].map(dt => {
            return Common.formatDate(dt, this.valueFormat[value])
          })
        ])
      } else {
        const valueDate = typeof this.dates[`${value}__date`] === 'string' ? this.dates[`${value}__date`] : Common.formatDate(this.dates[`${value}__date`], this.valueFormat[value])
        this.$emit(eventName, [label, valueDate])
      }
    },
    changeDateTypes() {
      const { label: preLabel } = this.dateType
      this.setDateType()
      this.dateTypes.forEach((item) => {
        const { value } = item
        if (!this.dates[`${value}__date`]) {
          this.setDateTypeValue(item)
        }
      })
      // 前后类型不一致，触发事件
      if (preLabel !== this.dateType.label) this.handleDateChange('switch-date-type')
    }
  }
}
</script>

<style lang="scss" scoped>
.date-picker {
  display: flex;
  span {
    color: #262626;
    font-size: 14px;
  }
  .el-select {
    width: 100px;
    margin-right: 5px;
  }
  :deep(.el-date-editor) {
    &:not(.el-range-editor) {
      width: 160px;
    }
    &.el-range-editor {
      width: 240px;
    }
  }
}
</style>
