<template>
  <div class="prevent-copy-test">
    <div class="header">
      <h1>防复制功能测试页面</h1>
      <p>测试页面用于验证防复制功能是否正常工作</p>
    </div>

    <!-- 控制按钮 -->
    <div class="controls">
      <el-button @click="enableGlobal" type="primary">启用全局防复制</el-button>
      <el-button @click="disableGlobal" type="default">禁用全局防复制</el-button>
      <el-button @click="checkStatus" type="info">检查状态</el-button>
    </div>

    <!-- 测试区域1：普通内容 -->
    <div class="test-section">
      <h3>测试区域1：普通内容（可复制）</h3>
      <p>这是普通的文本内容，应该可以正常选择和复制。</p>
      <p>请尝试：</p>
      <ul>
        <li>选择这些文字</li>
        <li>右键查看菜单</li>
        <li>使用Ctrl+C复制</li>
        <li>使用Ctrl+A全选</li>
      </ul>
      <img src="/favicon.ico" alt="普通图片" style="width: 32px; height: 32px; margin: 10px;">
    </div>

    <!-- 测试区域2：受保护内容 -->
    <div class="test-section protected" v-prevent-copy>
      <h3>测试区域2：受保护内容（禁止复制）</h3>
      <p>这是受保护的文本内容，应该无法选择和复制。</p>
      <p>请尝试：</p>
      <ul>
        <li>尝试选择这些文字（应该无法选择）</li>
        <li>右键查看菜单（应该被禁用）</li>
        <li>使用Ctrl+C复制（应该无效）</li>
        <li>使用Ctrl+A全选（应该无效）</li>
      </ul>
      <img src="/favicon.ico" alt="受保护图片" style="width: 32px; height: 32px; margin: 10px;">
      
      <!-- 输入框测试 -->
      <div style="margin-top: 20px;">
        <h4>输入框测试（应该正常工作）：</h4>
        <el-input v-model="testInput" placeholder="这里应该可以正常输入和复制"></el-input>
      </div>
    </div>

    <!-- 测试区域3：动态保护 -->
    <div class="test-section" v-prevent-copy="dynamicProtection">
      <h3>测试区域3：动态保护内容</h3>
      <p>这个区域的保护状态可以动态切换。</p>
      <p>当前状态：{{ dynamicProtection ? '受保护' : '不受保护' }}</p>
      <el-button @click="toggleDynamic" :type="dynamicProtection ? 'danger' : 'success'">
        {{ dynamicProtection ? '禁用' : '启用' }}区域保护
      </el-button>
    </div>

    <!-- 测试结果显示 -->
    <div class="test-results">
      <h3>测试结果</h3>
      <div class="status-info">
        <p><strong>全局防复制状态：</strong>{{ globalStatus ? '已启用' : '已禁用' }}</p>
        <p><strong>动态保护状态：</strong>{{ dynamicProtection ? '已启用' : '已禁用' }}</p>
        <p><strong>最后更新时间：</strong>{{ lastUpdateTime }}</p>
      </div>
    </div>

    <!-- 使用说明 -->
    <div class="instructions">
      <h3>使用说明</h3>
      <ol>
        <li>点击"启用全局防复制"按钮，整个页面将被保护</li>
        <li>点击"禁用全局防复制"按钮，恢复正常状态</li>
        <li>测试区域2始终受保护，无论全局状态如何</li>
        <li>测试区域3的保护状态可以动态切换</li>
        <li>输入框在受保护区域内仍然可以正常使用</li>
      </ol>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PreventCopyTest',
  data() {
    return {
      globalStatus: false,
      dynamicProtection: false,
      testInput: '测试输入内容',
      lastUpdateTime: ''
    }
  },
  mounted() {
    this.updateStatus()
    this.updateTime()
  },
  methods: {
    // 启用全局防复制
    enableGlobal() {
      this.$preventCopy.enable()
      this.updateStatus()
      this.updateTime()
      this.$message.success('全局防复制已启用')
    },
    
    // 禁用全局防复制
    disableGlobal() {
      this.$preventCopy.disable()
      this.updateStatus()
      this.updateTime()
      this.$message.success('全局防复制已禁用')
    },
    
    // 检查状态
    checkStatus() {
      this.updateStatus()
      this.updateTime()
      
      const config = this.$preventCopy.getConfig()
      const statusInfo = {
        全局状态: this.globalStatus ? '已启用' : '已禁用',
        动态保护: this.dynamicProtection ? '已启用' : '已禁用',
        配置信息: config
      }
      
      this.$alert(
        `<pre>${JSON.stringify(statusInfo, null, 2)}</pre>`,
        '防复制状态信息',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定'
        }
      )
    },
    
    // 切换动态保护
    toggleDynamic() {
      this.dynamicProtection = !this.dynamicProtection
      this.updateTime()
      this.$message.info(`区域保护已${this.dynamicProtection ? '启用' : '禁用'}`)
    },
    
    // 更新状态
    updateStatus() {
      this.globalStatus = this.$preventCopy.isEnabled()
    },
    
    // 更新时间
    updateTime() {
      this.lastUpdateTime = new Date().toLocaleString()
    }
  }
}
</script>

<style scoped>
.prevent-copy-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  font-family: 'Microsoft YaHei', sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
}

.header h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
}

.controls {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.controls .el-button {
  margin: 0 10px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
}

.test-section.protected {
  border-color: #dc3545;
  background: #fff5f5;
}

.test-section h3 {
  margin-top: 0;
  color: #333;
}

.test-results {
  margin-bottom: 30px;
  padding: 20px;
  background: #e8f5e8;
  border-radius: 8px;
  border: 2px solid #28a745;
}

.status-info p {
  margin: 10px 0;
  font-size: 16px;
}

.instructions {
  padding: 20px;
  background: #fff3cd;
  border: 2px solid #ffc107;
  border-radius: 8px;
}

.instructions h3 {
  margin-top: 0;
  color: #856404;
}

.instructions ol {
  margin: 15px 0;
  padding-left: 20px;
}

.instructions li {
  margin: 8px 0;
  line-height: 1.5;
}

ul, ol {
  padding-left: 20px;
}

li {
  margin: 5px 0;
}

img {
  border: 1px solid #ddd;
  border-radius: 4px;
}
</style>
