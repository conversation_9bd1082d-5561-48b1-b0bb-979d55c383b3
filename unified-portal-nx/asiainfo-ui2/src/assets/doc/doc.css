/************* header start *******/

.xd-header{
    padding-left: 310px;
    height: 50px;
    line-height: 50px;
    box-shadow: 0px 2px 5px #ccc;
    background-color: #fff;
    /*要小于doc-left*/
    z-index: 99;
    width: 100%;
    position: fixed;
}
.xd-header-placeholder{
    height: 50px;
}
.xd-header a{
    color: #666;
}

/************* header end *******/


/*******DOC start*********/
.doc-full-width .doc-content {
    margin-left: 0;
}

.doc-full-width .doc-left {
    display: none;
}

.doc {
}

.doc.left-collapsed .doc-left {
    margin-left: -250px;
}

.doc.left-collapsed .doc-content {
    margin-left: 0;
}

.doc.left-collapsed .doc-header {
    left: 0;
}

.doc .doc-header {
    height: 50px;
    position: fixed;
    top: 0;
    left: 250px;
    right: 0;
    z-index: 1000;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, .07);
    padding: 0 10px;
    transition: left .5s;
}

.doc .uk-sticky-placeholder {
    height: 50px;
    margin: 0;
}

.doc-header .doc-history {
    overflow-y: auto;
    max-height: 600px;
}

.doc-header .doc-history a {
    color: #1e87f0;
    text-decoration: underline;
}

.doc [class*=" el-icon-"], [class^=el-icon-] {
    margin: 0 4px;
}

.doc .doc-header li {
}

.doc .doc-header li a {
    display: block;
    color: #fff;
    line-height: 50px;
    padding: 0 10px;
    height: 50px;
}

.doc .doc-header .uk-table th {
    padding-top: 8px;
    padding-bottom: 8px;
}

.doc .doc-header .uk-table td {
    padding-top: 8px;
    padding-bottom: 8px;
}

.doc-left {
    width: 250px;
    border-right: 1px solid rgba(0, 0, 0, .07);
    height: 100%;
    position: fixed;
    top: 0;
    left: 60px;
    bottom: 0;
    background-color: #f5f6f9;
    z-index: 100;
    transition: margin-left .5s;
}

.doc-left .doc-search {
    width: 100%;
    height: 50px;
    line-height: 50px;
    background: #fff;
    padding-left: 14px;
    font-size: 14px;
    border: none;
    box-shadow: 0px 2px 5px #ccc;
}

.doc-left .searh-no-results {
    text-align: center;
    margin-top: 20px;
}

.doc-left .dl-doc-actions {
    padding-left: 10px;
    border-bottom: 1px solid #ccc;
    height: 50px;
}

.doc-left .dl-doc-actions a {
    font-size: 14px;
    line-height: 50px;
    padding-right: 12px;
    background: url("http://img.alicdn.com/tps/i3/TB1vn3AGXXXXXbFXXXXWXbjJFXX-7-4.png") right center no-repeat;
    background-position-y: 4px;
}

.doc-left .dl-doc-actions i {
    margin-right: 5px;
}

.doc-left .dl-doc-action {
    position: relative;
    margin-right: 20px;
}

.doc-left .dl-doc-action:hover > .dl-menus {
    display: block;
}

.doc-left .dl-background {
    position: absolute;
    left: 0;
    width: 100%;
    height: 35px;
    z-index: 1;
    display: block;
}

.doc-left .dl-placehoder {
    text-align: center;
    line-height: 40px;
}

.dl-docs .sortable-placeholder {
    height: 35px;
    background: #c0c0c0;
}

.dl-docs .dl-project-name {
    margin-top: 7px;
    font-weight: bold;
    font-size: 14px;
}

.dl-docs .divider {
    height: 1px;
    margin: 7px 0;
    overflow: hidden;
    background: rgba(0, 0, 0, .07)
}

.dl-docs .doc-name {
    line-height: 35px;
    height: 35px;
    cursor: pointer;
    margin-left: 15px;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.dl-docs .doc-name .item-name {
    position: relative;
    z-index: 2;
    color: #000;
    /*display: inline-block;*/
}

.dl-docs .doc-name i {
    color: #000;
}

.doc-left .doc-name:hover .dl-background {
    background: #d1dbe5;
}

.doc-left .icon-angeldownblock {
    display: inline-block;
}

.dl-docs .doc-name.api-folder-new {
    background: #e0e0e0;
    margin-top: 20px;
}

.dl-docs .doc-name a:hover {
    color: #0091ff;
    text-decoration: underline;
}

.dl-docs .doc-name.active .dl-background {
    background: #d1dbe5;
}

.dl-docs .doc-name.api-folder {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAHCAQAAACFbCRbAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAACYktHRAD/h4/MvwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAl2cEFnAAAABAAAAAcAwQJp2AAAACVJREFUCNctxjEBACAMA7BQsXgCs+UYV+K0qsIthGmY/uyVgQw83VQKohVhLVcAAAAldEVYdGRhdGU6Y3JlYXRlADIwMTQtMDktMjNUMTQ6MzM6NDgrMDg6MDBYHUu3AAAAJXRFWHRkYXRlOm1vZGlmeQAyMDE0LTA5LTIzVDE0OjMzOjQ4KzA4OjAwKUDzCwAAAABJRU5ErkJggg==") 8px 12px no-repeat;
}

.dl-docs .doc-name.api-folder.open {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAcAAAAECAQAAADoz+32AAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAACYktHRAD/h4/MvwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAl2cEFnAAAABwAAAAQAbLtItQAAACdJREFUCNdNiMENAEAIg6jDdienrQ9zOfkQUIdDWT+sgjcsIITQWQ/KugrMocu3oAAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAxNC0wOS0yM1QxNDozMzo0OCswODowMFgdS7cAAAAldEVYdGRhdGU6bW9kaWZ5ADIwMTQtMDktMjNUMTQ6MzM6NDgrMDg6MDApQPMLAAAAAElFTkSuQmCC");
    background-position-y: 15px;
}
.dl-docs .doc-name:hover .icon-angeldownblock {
    opacity: 1;
}

.dl-docs .icon-angeldownblock {
    margin-right: 5px;
    position: relative;
    z-index: 10;
    opacity: 0;
}
.dl-docs .icon-chevron-right{
    position: absolute;
    right: 1px;
    z-index: 10;
}

.dl-content {
    position: relative;
    overflow-y: auto;
}

.dl-menus, .dl-menus .sub {
    width: 130px;
    position: absolute;
    z-index: 100;
    background-color: #fff;
    box-shadow: 3px 3px 10px #ccc;
}

.dl-menus .sub {
    display: none;
}

.dl-docs-sub .dl-doc {
    padding-left: 15px;
}

.dl-menus li {
    position: relative;
    cursor: pointer;
}

.dl-menus > li:hover > .sub {
    display: block
}

.dl-menus .dl-menu-name {
    padding: 5px 0 5px 20px;
}

.dl-menus .dl-menu-name.folder {
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAHCAQAAACFbCRbAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAACYktHRAD/h4/MvwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAl2cEFnAAAABAAAAAcAwQJp2AAAACVJREFUCNctxjEBACAMA7BQsXgCs+UYV+K0qsIthGmY/uyVgQw83VQKohVhLVcAAAAldEVYdGRhdGU6Y3JlYXRlADIwMTQtMDktMjNUMTQ6MzM6NDgrMDg6MDBYHUu3AAAAJXRFWHRkYXRlOm1vZGlmeQAyMDE0LTA5LTIzVDE0OjMzOjQ4KzA4OjAwKUDzCwAAAABJRU5ErkJggg==") right center no-repeat;
    background-position-x: 110px;
}

.dl-menus .dl-menu-name:hover {
    background-color: #ddecf4;
    color: #1e87f0;
}

.dl-menus .sub {
    left: 130px;
    top: 0;
    display: none;
}

.doc-content {
    margin-left: 310px;
    padding:10px;
    position: relative;
    overflow-x: hidden;
    transition: margin-left .5s;
    min-height: 500px;
}

.doc-content-header-edit {
    line-height: 50px;
    font-size: 20px;
    width: 300px;
    border: none;
}

.doc-content-header .doc-ops a {
    display: inline-block;
    text-align: center;
    line-height: 50px;
    height: 50px;
    font-size: 18px;
    color: #888;
    text-decoration: none;
}

.doc-content-header .doc-ops a:hover {
    color: #1e87f0;
}

.doc-content-header .doc-content-title {
    font-size: 20px;
    color: rgb(126, 136, 139);
}

.doc-content .doc-save-button {
    width: 80px;
    height: 80px;
    background-color: #db4437;
    color: #fff;
    position: fixed;
    bottom: 10px;
    left: 50%;
    border: none;
    outline: none;
    border-radius: 100%;
    cursor: pointer;
}
.doc-content .doc-save-button:focus{
    background-color: darkred;
}

.doc-name-box {
    margin: 10px 0;
}

.doc-name-box input[type='text'] {
    width: 100%;
    padding: 10px;
    font-size: 24px;
    font-weight: bold;
    letter-spacing: 2px;
}

.doc-history-comment {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    max-width: 300px;
}

#api-description {
    padding: 10px 12px;
}

#ace-editor-box{
    min-height: 250px;
}

body.modal-open{
    overflow: hidden;
}
.modal{
    /*display: none;*/
    position: fixed;
    left:0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    z-index: 1000;
    overflow-y: auto;
}
.modal-close{
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 40px;
}
.modal-layout1{
    width: 300px;
    margin: 100px auto;
}
.modal-layout1 .title,.modal-layout2 .title{
    font-size: 24px;
    font-weight:bold;
    margin: 0 0 50px 0;
    text-align: center;
    padding: 0!important;
}
.modal-layout1 .actions,.modal-layout2 .actions{
    margin-top: 30px;
}

.modal-layout2{
    width: 350px;
    margin: 100px auto;
}
.modal-layout3{
    width: 500px;
    margin: 100px auto;
}
.modal-layout-box{
    width: 500px;
    border: 1px solid #e5e5e5;
    box-shadow: 2px 2px 5px #ccc;
    margin: 100px auto;
    padding: 15px;
}
.modal-layout-box .title{
    padding: 0!important;
    font-weight: bold;
    margin: 10px 0;
}
.env-modal{

}
.env-modal .hint{
    color: #01A304!important;
    background-color: #dff0d8;
    padding: 10px;
}
.env-modal .text{
    border: none;
    border-bottom: 1px solid #e0e0e0;
}
.env-modal .text:focus{
    border-bottom-color: #0f6ecd;
}
.env-modal .item{
    padding: 5px 0!important;
    border-bottom: none!important;
}

.api-modules-tab{
    position: fixed;
    z-index: 99;
    top:0;
    left: 45%;
    background:#fff;
}
.api-modules-tab a{
    border-bottom:2px solid #fff;
    line-height: 50px;
    height: 50px;
    display: inline-block;
    padding: 0 10px;
    color: #000;
}
.api-modules-tab a:hover{
    color: #db4437;
}
.api-modules-tab a.active{
    border-color: #db4437;
    color: #db4437!important;
}
.api-env-vars{
    color: #919191;
    margin-top: 5px;
}
.api-env-vars span{
    padding: 4px 8px;
    cursor: pointer;
    background-color: #CACACA;
    color: #383838;
    border-radius: 4px;
    margin-right: 5px;
}
.doc-item-section-title{
    margin:10px 0px 5px 0px;
}
/*******DOC end*********/








/********* SIDEBAR ************/



.db-left{
}
.db-left.layer .db-left-content,.db-left.layer .db-left-layer{
    display: block;
}


.db-left .logo{
    margin-top: 50px;
}
.db-left-content{
    position: fixed;
    width: 250px;
    top:0;
    left:60px;
    height: 100%;
    overflow-y: auto;
    z-index: 101;
    background-color: #fff;
}
.sidebar{
    left: 60px;
}
.dashboard.max .dlc1{
    display: none;
}
.dashboard.max .sidebar{
    display: block;
}
.sidebar .db-left-content{
    left: 60px;
}
.dbl-projects{
    display: block;
}
body.profile .dbl-projects{
    display: none;
}

.db-left-layer{
    background-color: rgba(138, 138, 138, 0.5);
    position: fixed;
    top:0;
    left:60px;
    height: 100%;
    width: 100%;
    overflow-y: auto;
    z-index: 100;
}
.db-left-bar{
    background-color: #2e2d2b;
    color: #fff;
    width: 60px;
    height: 100%;
    position: fixed;
    z-index: 999;
    left: 0;
    top: 0;
    /*overflow-y: auto;*/
}
.db-left-bar .logo img{
    width: 50px;
}
.db-left .db-left-bar .db-item{
    padding-left: 0;
}
.db-left .db-left-bar .db-item a{
    color: #a2a2a2;
    text-decoration: none;
}
.db-left .db-left-bar .db-item a:hover,.db-left .db-left-bar .db-item a.active{
    color: #fff;
}
.db-left-bar .db-item:hover{
    background-color: inherit;
}
.db-left-bar .db-item:hover .sub-ul{
    display: block;
}
.db-left .db-left-bar .db-item i{
    font-size: 30px;
    margin-right: inherit;
}

.db-left .db-item .sub-ul{
    display: none;
    width: 100px;
    background-color: #2e2d2b;
    position: absolute;
    left: 60px;
    border-left:1px solid #3a3939;
}

.db-left-search{
    margin: 40px 0 5px 25px;
}
.db-left-search [type='text']{
    border: none;
    line-height: 30px;
    height: 30px;
    margin-left: 3px;
}
.db-left-search i{
    font-size: 20px;
    color: #757575;
}
.db-search-box{
    margin: 30px auto;
    width: 80%;
    display: block;
}
.db-search-box input{
    border-color: #E5E5E5;
}
.db-left .db-item{
    display: -webkit-flex;
    display: flex;
    padding-left: 26px;
    margin-bottom: 10px;}

.db-left .db-item a{
    -webkit-flex: 3;
    flex: 3;
    height: 40px;
    width: 50%;
    line-height: 40px;
}
.db-left .db-item .shoucang{
    -webkit-flex: 1;
    flex: 1;
    display: none;
    text-align: center;
}
.db-left .db-item.active{
    background-color: #fff;
}
.db-left .db-item:hover .shoucang{
    display: block;
    color: #00b290;
}
.db-left .db-item i{
    margin-right: 10px;
}
.bd-project-title{
    padding-left: 26px;
    font-size: 16px;
    margin: 30px 0 20px 0;
}
.db-right{
    margin-left: 250px
}
.dashboard.max .db-right{
    margin-left: 60px;
}

.db-right .db-nav{
    display: block;
    border-bottom: 1px solid #e1e1e1;
}
.db-nav .db-back{
    border-right:1px solid #e1e1e1;
    margin:0 10px 0 5px;
}
.db-nav .db-item{
    position: relative;
}
.db-nav a{
    color: #666;
    display: block;
    height: 50px;
    line-height: 50px;
    padding: 0 15px;
}
.db-nav .db-item:hover{
    background-color: #F1F1F1;
}
.db-nav .page-name{
    font-weight: bold;
    font-size: 14px;
}
.db-right .db-nav a:hover{
    color: #000;
}
.db-nav .db-user-logo{
    width: 30px;
    height: 30px;
    border-radius: 100%;
    margin-right: 8px;
    margin-top: 10px;
}
.db-nav .db-msg{
    position: relative;
    padding-right: 20px;
}
.db-nav .db-msg .db-subscript{
    position: absolute;
    right: 10px;
    top: 5px;
    font-size: 12px;
    border-radius: 100%;
    background: #d43b49;
    color: #fff;
    display: block;
    height: 20px;
    width: 20px;
    padding: 0;
    text-align: center;
}

.db-item-sub{
    background: #fff;
    border: 1px solid #eaeaea;
    position: absolute;
    right: 0;
    z-index: 12;
    top: 50px;
}
.db-item-sub a{
    width: 150px;
    height: 40px;
    line-height: 40px;
}
.db-item-sub i{
    color: #00B290;
    margin-right: 5px;
}
.db-item-sub .db-item{
    border-bottom: 1px solid #eaeaea;
}
.db-item-sub .db-item:last-child{
    border: none;
}
.db-item:hover .db-item-sub{
    /*display: block;*/
}
.db-nav-sub-profile{
    margin: 10px 0;
    line-height: 25px;
    width: 180px;
    overflow: hidden;
}
.db-nav-profile-name{
    font-size: 16px;
}
.db-item-sub .db-user-logo{
    height: 50px;
    width: 50px;
    margin: 10px 20px 10px 20px;
}
.db-item .db-profile-info{
    background: #fff!important;
    color: #666;
    width: 280px;
}
.db-item.profile .db-item{
    background: #f9f9f9;
}
.db-item.profile .db-item a{
    padding-left: 30px;
}
.db-nav-msg-box{
    padding: 0 10px;
    line-height: 40px;
    background: #e2e2e2;
    width: 300px;
}
.db-msg .db-item-sub{
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
}
.db-msg .db-item:not(.item-title){
    padding: 5px;
}
.db-msg .db-item:hover{
    background: none;
}
.db-msg .db-item a:hover{
    color: #0759ff;
}
.db-msg .db-item a{
    font-size: 12px;
    color: #06c;
    height: initial;
    line-height: 16px;
    width: 100%;
    padding: 5px 8px 0 8px;
    box-sizing: border-box;
}
.db-main{
    padding: 60px 0 0 80px;
}
.db-view-form{
    width: 600px;
}
.sidebar-o-op{
    position: absolute;
    width: 100%;
    bottom: 50px;
}

.share-list li{margin-bottom: 10px;}
.share-list .uk-input{
    width: 136px;
    position: absolute;
    right: 30px;
    height: 25px;
    margin-top: -5px;
    display: none;
}
.share-list li.editing .uk-input{
    display: block;
}
.share-list .share-doc-names,.share-list .share-name{
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 380px;
}
.share-creation-box .uk-form-controls{
    margin-right: auto!important;
    margin-left: 115px!important;
}
.share-creation-box .uk-form-label{
    width: 100px!important;
}
.share-creation-box .uk-form-label:first-child{
    margin-top: 0!important;
}
/************* SIDEBAR END ***********/


